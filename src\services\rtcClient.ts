/**
 * RTC客户端服务
 */

import VERTC, { MediaType, StreamIndex } from '@volcengine/rtc';
import { RTCClient } from '../types/rtc';

interface RTCClientConfig {
  appId: string;
}

interface StreamOptions {
  audio: boolean;
  video: boolean;
}

interface RTCClientProps {
  config: RTCClientConfig;
  streamOptions: StreamOptions;
  handleUserPublishStream: (event: any) => void;
  handleUserUnpublishStream: (event: any) => void;
  handleUserStartVideoCapture: (event: any) => void;
  handleUserStopVideoCapture: (event: any) => void;
  handleEventError: (error: any, VERTC: any) => void;
  handleUserJoin: (event: any) => void;
  handleUserLeave: (event: any) => void;
  handleAutoPlayFail: (event: any) => void;
  handlePlayerEvent: (event: any) => void;
}

export default class RtcClient implements RTCClient {
  config: RTCClientConfig;
  streamOptions: StreamOptions;
  engine: any;
  handleUserPublishStream: (event: any) => void;
  handleUserUnpublishStream: (event: any) => void;
  handleUserStartVideoCapture: (event: any) => void;
  handleUserStopVideoCapture: (event: any) => void;
  handleEventError: (error: any, VERTC: any) => void;
  handleUserJoin: (event: any) => void;
  handleUserLeave: (event: any) => void;
  handleAutoPlayFail: (event: any) => void;
  handlePlayerEvent: (event: any) => void;
  SDKVERSION: string;

  constructor(props: RTCClientProps) {
    this.config = props.config;
    this.streamOptions = props.streamOptions;
    this.engine = VERTC.createEngine(props.config.appId);
    this.handleUserPublishStream = props.handleUserPublishStream;
    this.handleUserUnpublishStream = props.handleUserUnpublishStream;
    this.handleUserStartVideoCapture = props.handleUserStartVideoCapture;
    this.handleUserStopVideoCapture = props.handleUserStopVideoCapture;
    this.handleEventError = props.handleEventError;
    this.handleUserJoin = props.handleUserJoin;
    this.handleUserLeave = props.handleUserLeave;
    this.handleAutoPlayFail = props.handleAutoPlayFail;
    this.handlePlayerEvent = props.handlePlayerEvent;
    this.SDKVERSION = VERTC.getSdkVersion();
    this.bindEngineEvents();
  }

  init() {
    // 初始化方法
  }

  bindEngineEvents() {
    this.engine.on(VERTC.events.onUserPublishStream, this.handleUserPublishStream);
    this.engine.on(VERTC.events.onUserUnpublishStream, this.handleUserUnpublishStream);
    this.engine.on(VERTC.events.onUserStartVideoCapture, this.handleUserStartVideoCapture);
    this.engine.on(VERTC.events.onUserStopVideoCapture, this.handleUserStopVideoCapture);
    this.engine.on(VERTC.events.onUserJoined, this.handleUserJoin);
    this.engine.on(VERTC.events.onUserLeave, this.handleUserLeave);
    this.engine.on(VERTC.events.onAutoplayFailed, (events: any) => {
      console.log('VERTC.events.onAutoplayFailed', events.userId);
      this.handleAutoPlayFail(events);
    });
    this.engine.on(VERTC.events.onPlayerEvent, this.handlePlayerEvent);
    this.engine.on(VERTC.events.onError, (e: any) => this.handleEventError(e, VERTC));
  }

  async setRemoteVideoPlayer(remoteUserId: string, domId: string) {
    await this.engine.subscribeStream(remoteUserId, MediaType.AUDIO_AND_VIDEO);
    await this.engine.setRemoteVideoPlayer(StreamIndex.STREAM_INDEX_MAIN, {
      userId: remoteUserId,
      renderDom: domId,
    });
  }

  removeEventListener() {
    this.engine.off(VERTC.events.onUserPublishStream, this.handleUserPublishStream);
    this.engine.off(VERTC.events.onUserUnpublishStream, this.handleUserUnpublishStream);
    this.engine.off(VERTC.events.onUserStartVideoCapture, this.handleUserStartVideoCapture);
    this.engine.off(VERTC.events.onUserStopVideoCapture, this.handleUserStopVideoCapture);
    this.engine.off(VERTC.events.onUserJoined, this.handleUserJoin);
    this.engine.off(VERTC.events.onUserLeave, this.handleUserLeave);
    this.engine.off(VERTC.events.onAutoplayFailed, this.handleAutoPlayFail);
    this.engine.off(VERTC.events.onPlayerEvent, this.handlePlayerEvent);
    this.engine.off(VERTC.events.onError, this.handleEventError);
  }

  join(token: string | null, roomId: string, uid: string) {
    return this.engine.joinRoom(
      token,
      roomId,
      {
        userId: uid,
      },
      {
        isAutoPublish: true,
        isAutoSubscribeAudio: true,
        isAutoSubscribeVideo: true,
      }
    );
  }

  checkPermission() {
    return VERTC.enableDevices();
  }

  async getDevices() {
    const devices = await VERTC.enumerateDevices();
    return {
      audioInputs: devices.filter((i: any) => i.deviceId && i.kind === 'audioinput'),
      videoInputs: devices.filter((i: any) => i.deviceId && i.kind === 'videoinput'),
    };
  }

  async createLocalStream(userId: string, callback?: (result: any) => void) {
    const devices = await this.getDevices();
    const devicesStatus = {
      video: 1,
      audio: 1,
    };

    if (!devices.audioInputs.length && !devices.videoInputs.length) {
      callback?.({
        code: -1,
        msg: '设备权限获取失败',
        devicesStatus: {
          video: 0,
          audio: 0,
        },
      });
      return;
    }

    if (this.streamOptions.audio && devices.audioInputs.length) {
      await this.engine.startAudioCapture(devices.audioInputs[0].deviceId);
    } else {
      devicesStatus['audio'] = 0;
      this.engine.unpublishStream(MediaType.AUDIO);
    }

    if (this.streamOptions.video && devices.videoInputs.length) {
      await this.engine.startVideoCapture(devices.videoInputs[0].deviceId);
    } else {
      devicesStatus['video'] = 0;
      this.engine.unpublishStream(MediaType.VIDEO);
    }

    this.engine.setLocalVideoPlayer(StreamIndex.STREAM_INDEX_MAIN, {
      renderDom: 'local-player',
      userId,
    });

    this.engine.publishStream(MediaType.AUDIO_AND_VIDEO);

    callback?.({
      code: 0,
      msg: '设备获取成功',
      devicesStatus,
    });
  }

  async changeAudioState(isMicOn: boolean) {
    if (isMicOn) {
      await this.engine.publishStream(MediaType.AUDIO);
    } else {
      await this.engine.unpublishStream(MediaType.AUDIO);
    }
  }

  async changeVideoState(isVideoOn: boolean) {
    if (isVideoOn) {
      await this.engine.startVideoCapture();
    } else {
      await this.engine.stopVideoCapture();
    }
  }

  async leave() {
    await Promise.all([
      this.engine?.stopVideoCapture(),
      this.engine?.stopAudioCapture(),
    ]);
    await this.engine?.unpublishStream(MediaType.AUDIO_AND_VIDEO).catch(console.warn);
    this.engine.leaveRoom();
  }

  // 实现接口中的其他方法
  async publishStream() {
    await this.engine.publishStream(MediaType.AUDIO_AND_VIDEO);
  }

  async unpublishStream() {
    await this.engine.unpublishStream(MediaType.AUDIO_AND_VIDEO);
  }

  subscribe() {
    // 订阅实现
  }

  on() {
    // 事件监听实现
  }

  off() {
    // 事件移除实现
  }

  setupLocalVideoPlayer() {
    // 本地视频播放器设置
  }
}
