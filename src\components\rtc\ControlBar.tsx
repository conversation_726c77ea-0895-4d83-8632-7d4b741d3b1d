/**
 * RTC控制栏组件
 */

import React from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';

interface ControlBarProps {
  isMicOn: boolean;
  isVideoOn: boolean;
  onToggleMic: () => void;
  onToggleVideo: () => void;
  onLeaveRoom: () => void;
}

const ControlBarWrapper = styled.div`
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(0, 0, 0, 0.8);
  padding: 12px 24px;
  border-radius: 24px;
  backdrop-filter: blur(10px);
  z-index: 1000;
`;

const ControlButton = styled.button<{ active?: boolean; danger?: boolean }>`
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  ${props => props.danger ? `
    background: #ff4757;
    color: white;
    
    &:hover {
      background: #ff3838;
    }
  ` : props.active ? `
    background: #2f3542;
    color: white;
    
    &:hover {
      background: #40485a;
    }
  ` : `
    background: #5f27cd;
    color: white;
    
    &:hover {
      background: #4834d4;
    }
  `}
  
  svg {
    width: 20px;
    height: 20px;
  }
`;

const ControlBar: React.FC<ControlBarProps> = ({
  isMicOn,
  isVideoOn,
  onToggleMic,
  onToggleVideo,
  onLeaveRoom
}) => {
  const { t } = useTranslation();

  return (
    <ControlBarWrapper>
      <ControlButton
        active={!isMicOn}
        onClick={onToggleMic}
        title={isMicOn ? t('rtc.muteMic', '静音') : t('rtc.unmuteMic', '取消静音')}
      >
        {isMicOn ? (
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
            <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
          </svg>
        ) : (
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28zm-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18l5.98 5.99zM4.27 3L3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.24-5.3-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73 4.27 3z"/>
          </svg>
        )}
      </ControlButton>

      <ControlButton
        active={!isVideoOn}
        onClick={onToggleVideo}
        title={isVideoOn ? t('rtc.turnOffVideo', '关闭摄像头') : t('rtc.turnOnVideo', '开启摄像头')}
      >
        {isVideoOn ? (
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
          </svg>
        ) : (
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M21 6.5l-4 4V7c0-.55-.45-1-1-1H9.82l-3.28-3.28c.46-.17.96-.22 1.46-.22h8c.55 0 1 .45 1 1v3.5l4-4v11l-1.43-1.43L21 6.5zM3.27 2L2 3.27 4.73 6H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.21 0 .39-.08.55-.18L19.73 21 21 19.73 3.27 2z"/>
          </svg>
        )}
      </ControlButton>

      <ControlButton
        danger
        onClick={onLeaveRoom}
        title={t('rtc.leaveRoom', '离开房间')}
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"/>
        </svg>
      </ControlButton>
    </ControlBarWrapper>
  );
};

export default ControlBar;
