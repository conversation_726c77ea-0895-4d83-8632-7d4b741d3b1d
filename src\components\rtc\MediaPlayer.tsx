/**
 * RTC媒体播放器组件
 */

import React from 'react';
import styled from 'styled-components';

export interface VideoPlayerProps {
  userId: string;
  isLocal?: boolean;
}

const PlayerContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
`;

const UserLabel = styled.span`
  color: #fff;
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
`;

const MediaPlayer: React.FC<VideoPlayerProps> = ({ userId, isLocal = false }) => {
  const playerId = isLocal ? 'local-player' : `remoteStream_${userId}`;
  
  return (
    <PlayerContainer
      className={isLocal ? 'localStream' : 'remoteStream'}
      id={playerId}
    >
      <UserLabel>
        {isLocal ? '我' : userId}
      </UserLabel>
    </PlayerContainer>
  );
};

export default MediaPlayer;
