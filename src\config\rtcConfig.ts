/**
 * 火山引擎RTC配置文件
 * 用于视频通话功能
 */

const rtcConfig = {
  // 应用ID - 需要在火山引擎控制台获取
  appId: process.env.VITE_RTC_APP_ID || 'yourAppId',
  
  // 默认房间ID
  roomId: 'tarot-video-room',
  
  // Token配置 - 在实际使用中应该从服务器获取
  tokens: [
    {
      userId: 'user1',
      token: process.env.VITE_RTC_TOKEN_USER1 || 'yourToken1',
    },
    {
      userId: 'user2', 
      token: process.env.VITE_RTC_TOKEN_USER2 || 'yourToken2',
    }
  ],
  
  // 流配置选项
  streamOptions: {
    video: {
      width: 640,
      height: 480,
      frameRate: 15,
      bitrate: 500
    },
    audio: {
      sampleRate: 48000,
      channels: 1,
      bitrate: 64
    }
  }
};

export default rtcConfig;
