/**
 * 加入房间组件
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useRTC } from '../../contexts/RTCContext';
import { useUser } from '../../hooks/useUser';

interface JoinRoomProps {
  onJoinRoom: () => void;
}

const Container = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
`;

const Card = styled.div`
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
`;

const Title = styled.h2`
  text-align: center;
  margin-bottom: 32px;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
`;

const FormGroup = styled.div`
  margin-bottom: 24px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  color: #34495e;
  font-weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: #5f27cd;
  }
  
  &::placeholder {
    color: #95a5a6;
  }
`;

const Button = styled.button`
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
`;

const ErrorMessage = styled.div`
  color: #e74c3c;
  text-align: center;
  margin-top: 16px;
  padding: 12px;
  background: #fdf2f2;
  border-radius: 8px;
  border: 1px solid #fecaca;
`;

const JoinRoom: React.FC<JoinRoomProps> = ({ onJoinRoom }) => {
  const { t } = useTranslation();
  const { user } = useUser();
  const { userId, roomId, joinFailReason, setUserId, setRoomId } = useRTC();
  const [isJoining, setIsJoining] = useState(false);

  const handleJoin = async () => {
    if (!userId.trim() || !roomId.trim()) {
      return;
    }

    setIsJoining(true);
    try {
      // 这里可以添加加入房间前的验证逻辑
      onJoinRoom();
    } catch (error) {
      console.error('加入房间失败:', error);
    } finally {
      setIsJoining(false);
    }
  };

  return (
    <Container>
      <Card>
        <Title>{t('rtc.joinVideoCall', '加入视频通话')}</Title>
        
        <FormGroup>
          <Label>{t('rtc.userId', '用户ID')}</Label>
          <Input
            type="text"
            value={userId}
            onChange={(e) => setUserId(e.target.value)}
            placeholder={user?.username || t('rtc.enterUserId', '请输入用户ID')}
          />
        </FormGroup>

        <FormGroup>
          <Label>{t('rtc.roomId', '房间ID')}</Label>
          <Input
            type="text"
            value={roomId}
            onChange={(e) => setRoomId(e.target.value)}
            placeholder={t('rtc.enterRoomId', '请输入房间ID')}
          />
        </FormGroup>

        <Button
          onClick={handleJoin}
          disabled={!userId.trim() || !roomId.trim() || isJoining}
        >
          {isJoining ? t('rtc.joining', '正在加入...') : t('rtc.joinRoom', '加入房间')}
        </Button>

        {joinFailReason && (
          <ErrorMessage>
            {t('rtc.joinFailed', '加入失败')}: {joinFailReason}
          </ErrorMessage>
        )}
      </Card>
    </Container>
  );
};

export default JoinRoom;
