/**
 * 会议界面组件
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { MediaType } from '@volcengine/rtc';
import { useRTC } from '../../contexts/RTCContext';
import RtcClient from '../../services/rtcClient';
import MediaPlayer from './MediaPlayer';
import ControlBar from './ControlBar';
import rtcConfig from '../../config/rtcConfig';

const Container = styled.div`
  width: 100%;
  height: 100vh;
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  gap: 8px;
  background: #1a1a1a;
`;

const VideoItem = styled.div`
  flex: 1;
  width: calc(50% - 8px);
  min-width: calc(50% - 8px);
  max-width: calc(50% - 8px);
  height: calc(50% - 8px);
  position: relative;
  border-radius: 8px;
  overflow: hidden;
`;

const EmptySlot = styled.div`
  flex: 1;
  width: calc(50% - 8px);
  min-width: calc(50% - 8px);
  max-width: calc(50% - 8px);
  height: calc(50% - 8px);
  background: #2c2c2c;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
`;

interface RemoteUser {
  userId: string;
  hasAudio: boolean;
  hasVideo: boolean;
}

const Meeting: React.FC = () => {
  const { t } = useTranslation();
  const { roomId, userId, setJoin, setJoinFailReason } = useRTC();
  const [isMicOn, setMicOn] = useState<boolean>(true);
  const [isVideoOn, setVideoOn] = useState<boolean>(true);
  const [remoteUsers, setRemoteUsers] = useState<RemoteUser[]>([]);
  const rtc = useRef<RtcClient>();

  // 事件处理函数
  const handleUserPublishStream = useCallback((event: any) => {
    console.log('用户发布流:', event);
    const { userId: remoteUserId, mediaType } = event;
    
    setRemoteUsers(prev => {
      const existingUser = prev.find(user => user.userId === remoteUserId);
      if (existingUser) {
        return prev.map(user => 
          user.userId === remoteUserId 
            ? { 
                ...user, 
                hasAudio: mediaType === MediaType.AUDIO || mediaType === MediaType.AUDIO_AND_VIDEO,
                hasVideo: mediaType === MediaType.VIDEO || mediaType === MediaType.AUDIO_AND_VIDEO
              }
            : user
        );
      } else {
        return [...prev, {
          userId: remoteUserId,
          hasAudio: mediaType === MediaType.AUDIO || mediaType === MediaType.AUDIO_AND_VIDEO,
          hasVideo: mediaType === MediaType.VIDEO || mediaType === MediaType.AUDIO_AND_VIDEO
        }];
      }
    });

    // 设置远程视频播放器
    if (rtc.current && (mediaType === MediaType.VIDEO || mediaType === MediaType.AUDIO_AND_VIDEO)) {
      rtc.current.setRemoteVideoPlayer(remoteUserId, `remoteStream_${remoteUserId}`);
    }
  }, []);

  const handleUserUnpublishStream = useCallback((event: any) => {
    console.log('用户取消发布流:', event);
    const { userId: remoteUserId } = event;
    
    setRemoteUsers(prev => prev.filter(user => user.userId !== remoteUserId));
  }, []);

  const handleUserJoin = useCallback((event: any) => {
    console.log('用户加入:', event);
  }, []);

  const handleUserLeave = useCallback((event: any) => {
    console.log('用户离开:', event);
    const { userId: remoteUserId } = event;
    setRemoteUsers(prev => prev.filter(user => user.userId !== remoteUserId));
  }, []);

  const handleUserStartVideoCapture = useCallback((event: any) => {
    console.log('用户开始视频捕获:', event);
  }, []);

  const handleUserStopVideoCapture = useCallback((event: any) => {
    console.log('用户停止视频捕获:', event);
  }, []);

  const handleEventError = useCallback((error: any, VERTC: any) => {
    console.error('RTC错误:', error);
    setJoinFailReason(JSON.stringify(error));
  }, [setJoinFailReason]);

  const handleAutoPlayFail = useCallback((event: any) => {
    console.log('自动播放失败:', event);
  }, []);

  const handlePlayerEvent = useCallback((event: any) => {
    console.log('播放器事件:', event);
  }, []);

  // 初始化RTC客户端
  useEffect(() => {
    if (!roomId || !userId) return;

    rtc.current = new RtcClient({
      config: { appId: rtcConfig.appId },
      streamOptions: { audio: true, video: true },
      handleUserPublishStream,
      handleUserUnpublishStream,
      handleUserStartVideoCapture,
      handleUserStopVideoCapture,
      handleEventError,
      handleUserJoin,
      handleUserLeave,
      handleAutoPlayFail,
      handlePlayerEvent
    });

    // 加入房间
    const joinRoom = async () => {
      try {
        let token = null;
        rtcConfig.tokens.forEach((item) => {
          if (item.userId === userId) {
            token = item.token;
          }
        });

        await rtc.current!.join(token, roomId, userId);
        await rtc.current!.createLocalStream(userId, (res: any) => {
          const { code, msg } = res;
          if (code === -1) {
            if (window.confirm(`${msg}, 是否跳转排查文档?`)) {
              window.location.href = 'https://www.volcengine.com/docs/6348/1356355';
            }
            setMicOn(false);
            setVideoOn(false);
          }
        });
      } catch (err: any) {
        console.error('加入房间失败:', err);
        leaveRoom(false);
        setJoinFailReason(JSON.stringify(err));
      }
    };

    joinRoom();

    return () => {
      if (rtc.current) {
        rtc.current.removeEventListener();
        rtc.current.leave();
      }
    };
  }, [roomId, userId, handleUserPublishStream, handleUserUnpublishStream, handleUserStartVideoCapture, handleUserStopVideoCapture, handleEventError, handleUserJoin, handleUserLeave, handleAutoPlayFail, handlePlayerEvent, setJoinFailReason]);

  // 控制函数
  const toggleMic = useCallback(async () => {
    if (rtc.current) {
      await rtc.current.changeAudioState(!isMicOn);
      setMicOn(!isMicOn);
    }
  }, [isMicOn]);

  const toggleVideo = useCallback(async () => {
    if (rtc.current) {
      await rtc.current.changeVideoState(!isVideoOn);
      setVideoOn(!isVideoOn);
    }
  }, [isVideoOn]);

  const leaveRoom = useCallback(async (shouldSetJoin = true) => {
    if (rtc.current) {
      await rtc.current.leave();
    }
    if (shouldSetJoin) {
      setJoin(false);
    }
  }, [setJoin]);

  // 渲染视频网格
  const renderVideoGrid = () => {
    const allUsers = [{ userId, isLocal: true }, ...remoteUsers.map(user => ({ ...user, isLocal: false }))];
    const slots = Array(4).fill(null);

    return slots.map((_, index) => {
      const user = allUsers[index];
      if (user) {
        return (
          <VideoItem key={user.userId}>
            <MediaPlayer userId={user.userId} isLocal={user.isLocal} />
          </VideoItem>
        );
      } else {
        return (
          <EmptySlot key={`empty-${index}`}>
            {t('rtc.waitingForUser', '等待用户加入...')}
          </EmptySlot>
        );
      }
    });
  };

  return (
    <>
      <Container>
        {renderVideoGrid()}
      </Container>
      
      <ControlBar
        isMicOn={isMicOn}
        isVideoOn={isVideoOn}
        onToggleMic={toggleMic}
        onToggleVideo={toggleVideo}
        onLeaveRoom={() => leaveRoom()}
      />
    </>
  );
};

export default Meeting;
