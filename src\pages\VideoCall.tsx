/**
 * 视频通话页面
 */

import React, { useMemo } from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useTheme } from '../contexts/ThemeContext';
import { RTCProvider, useRTC } from '../contexts/RTCContext';
import JoinR<PERSON> from '../components/rtc/JoinRoom';
import Meeting from '../components/rtc/Meeting';
import rtcConfig from '../config/rtcConfig';

const PageContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
`;

const Header = styled.div`
  padding: 0px 22px;
  width: 100%;
  height: 64px;
  background: #0a1e39;
  z-index: 1000;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const Logo = styled.div`
  display: flex;
  flex-direction: row;
  height: 38.4px;
  flex: 1;
  color: white;
  font-size: 18px;
  font-weight: 600;
  align-items: center;
`;

const RoomInfo = styled.div`
  text-align: center;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 600;
  line-height: 64px;
  color: #ffffff;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const SDKVersion = styled.span`
  float: right;
  color: #ffffff;
  font-size: 12px;
  line-height: 64px;
  font-weight: 600;
  font-family: PingFang SC;
`;

const ContentWrapper = styled.div`
  height: calc(100vh - 64px);
  background: #1a1a1a;
`;

// 内部组件，可以使用RTC上下文
const VideoCallContent: React.FC = () => {
  const { t } = useTranslation();
  const { hasJoin, roomId, setJoin } = useRTC();

  const handleJoinRoom = () => {
    setJoin(true);
  };

  return (
    <PageContainer>
      <Header>
        <Logo>
          {t('rtc.tarotVideoCall', '塔罗视频通话')}
        </Logo>
        {hasJoin ? (
          <RoomInfo>{roomId}</RoomInfo>
        ) : (
          <SDKVersion>RTC版本 v4.66.20</SDKVersion>
        )}
      </Header>
      
      <ContentWrapper>
        {hasJoin ? (
          <Meeting />
        ) : (
          <JoinRoom onJoinRoom={handleJoinRoom} />
        )}
      </ContentWrapper>
    </PageContainer>
  );
};

const VideoCall: React.FC = () => {
  const { t } = useTranslation();
  const { theme } = useTheme();

  // 检查登录信息
  const hasLogin = useMemo(() => {
    // 这里可以添加登录检查逻辑
    return true;
  }, []);

  return (
    <>
      <Helmet>
        <title>{t('rtc.videoCallTitle', '视频通话 - 塔罗占卜')}</title>
        <meta 
          name="description" 
          content={t('rtc.videoCallDescription', '与塔罗占卜师进行实时视频通话，获得更深入的占卜体验')} 
        />
      </Helmet>

      <RTCProvider>
        <VideoCallContent />
      </RTCProvider>
    </>
  );
};

export default VideoCall;
