{"name": "quick-start", "version": "0.1.0", "private": true, "dependencies": {"@volcengine/rtc": "~4.66.20", "antd": "^4.16.13", "query-string": "7.0.1", "react": "18.2.0", "react-dom": "18.2.0", "styled-components": "5.3.1", "uuid": "8.3.2", "web-vitals": "1.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "gitHooks": {"pre-commit": "lint-staged"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "git add"], "*.{js,jsx}": ["eslint --fix", "git add"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "5.11.4", "@testing-library/react": "11.1.0", "@testing-library/user-event": "12.1.10", "@types/jest": "26.0.15", "@types/node": "16.11.45", "@types/react-dom": "18.0.6", "@types/styled-components": "5.1.15", "@types/uuid": "8.3.1", "customize-cra": "1.0.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "lint-staged": "11.2.6", "prettier": "2.4.1", "react-app-rewired": "2.2.1", "react-scripts": "5.0.1", "typescript": "4.7.4"}, "resolutions": {"@types/react": "18.0.15"}}