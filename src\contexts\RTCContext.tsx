/**
 * RTC上下文
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { RTCContextType } from '../types/rtc';

const RTCContext = createContext<RTCContextType | undefined>(undefined);

interface RTCProviderProps {
  children: ReactNode;
}

export const RTCProvider: React.FC<RTCProviderProps> = ({ children }) => {
  const [hasJoin, setJoin] = useState<boolean>(false);
  const [joinFailReason, setJoinFailReason] = useState<string>('');
  const [userId, setUserId] = useState<string>('');
  const [roomId, setRoomId] = useState<string>('');

  const value: RTCContextType = {
    hasJoin,
    userId,
    roomId,
    joinFailReason,
    setUserId,
    setRoomId,
    setJoin,
    setJoinFailReason
  };

  return (
    <RTCContext.Provider value={value}>
      {children}
    </RTCContext.Provider>
  );
};

export const useRTC = (): RTCContextType => {
  const context = useContext(RTCContext);
  if (context === undefined) {
    throw new Error('useRTC must be used within a RTCProvider');
  }
  return context;
};
